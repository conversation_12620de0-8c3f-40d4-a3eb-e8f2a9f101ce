your_project_directory/
├── main.py # 主程序入口，负责初始化和主循环
├── constants.py # 存储所有常量定义 (模式代码, 状态码, 字符映射等)
├── app_state.py # 定义 AppState 类，用于管理程序运行时的共享状态
├── config_manager.py # 负责加载和保存 combined_config.json 配置文件
├── camera_manager.py # 封装摄像头初始化、参数设置和帧读取逻辑
├── led_detector.py # 包含 LED 样本采集、阈值计算和状态检测的逻辑
├── digit_detector.py # 包含数码管段检测、字符识别和缺陷检查的逻辑
├── ui_handler.py # 处理用户界面绘制、鼠标/键盘事件、模式切换和主逻辑协调
└── combined_config.json # 配置文件 (首次成功校准或保存设置后生成)