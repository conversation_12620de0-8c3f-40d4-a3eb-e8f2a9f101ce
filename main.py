import cv2
import time
import logging # 添加导入

from app_state import AppState
from constants import *
import config_manager
import camera_manager
import ui_handler # Import the UI handler

def main():
    # --- 日志设置 ---
    log_file = 'led_digit_detection.log' # 可以自定义日志文件名
    logging.basicConfig(filename=log_file,
                        level=logging.INFO, # 记录 INFO 及以上级别的信息
                        format='%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S',
                        encoding='utf-8') # 明确指定 UTF-8 编码
    print(f"日志将记录到: {log_file}")
    logging.info("程序启动")
    # --- 日志设置结束 ---

    # 1. 创建 AppState 实例
    app_state = AppState()
    app_state.prev_time = time.time() # 初始化 FPS 计时器

    # 2. 加载配置
    config_manager.load_config(app_state) # 会根据配置文件或用户输入设置初始模式

    # 3. 初始化摄像头
    if not camera_manager.initialize_camera(app_state):
        logging.error("摄像头初始化失败，程序退出。") # 添加错误日志
        print("摄像头初始化失败，程序退出。")
        return # 无法继续

    # 4. 创建窗口并设置鼠标回调
    cv2.namedWindow(MAIN_WINDOW, cv2.WINDOW_NORMAL)
    ui_handler.setup_mouse_callback(MAIN_WINDOW, app_state) # 使用 ui_handler 设置回调

    # 5. 主循环
    while app_state.running:
        # 调用 ui_handler 处理当前帧的逻辑和 UI 更新
        ui_handler.process_ui_and_logic(app_state)

        # 主循环也检查退出键 'q'，以防模式处理函数未捕获
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
             logging.info("用户按下 'q' 退出程序。")
             app_state.running = False

        # 检查窗口是否被用户关闭
        try:
             # 如果窗口已被关闭，getProperty 会引发错误
             if cv2.getWindowProperty(MAIN_WINDOW, cv2.WND_PROP_VISIBLE) < 1:
                  logging.info("检测到窗口关闭事件。")
                  print("检测到窗口关闭事件。")
                  app_state.running = False
        except cv2.error:
             # 捕获 OpenCV 错误，通常意味着窗口已关闭
             logging.warning("窗口句柄无效 (可能已关闭)，程序退出。")
             print("窗口句柄无效，程序退出。")
             app_state.running = False


    # 6. 清理资源
    logging.info("正在关闭程序...")
    print("正在关闭程序...")
    camera_manager.release_camera(app_state) # 释放摄像头
    cv2.destroyAllWindows()
    logging.info("程序正常结束") # 添加结束日志
    print("程序已退出。")

if __name__ == "__main__":
    main()
