import re
import os
from datetime import datetime

# 定义日志文件路径
log_file = 'led_digit_detection.log'
# 如果要指定绝对路径，可以取消下面这行的注释
# log_file = r'C:\Users\<USER>\Desktop\日志分析\led_detection.log'

def analyze_led_cycles():
    """
    分析LED日志文件，计算完美顺序点亮周期的数量
    
    完美周期定义:
    1. 从ROI 1开始，按顺序依次点亮ROI 2, ROI 3...直到ROI 16
    2. 每个ROI点亮区间内，只有该ROI为ON状态，其他ROI为OFF状态
    """
    # 检查文件是否存在
    if not os.path.exists(log_file):
        print(f"文件 {log_file} 不存在!")
        return
    
    start_time = datetime.now()
    print(f"开始时间: {start_time.strftime('%H:%M:%S')}")
    
    try:
        # 尝试使用UTF-8编码读取
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            # 如果UTF-8失败，尝试使用latin-1编码（可以读取任何字节）
            print("UTF-8编码读取失败，尝试使用latin-1编码...")
            with open(log_file, 'r', encoding='latin-1') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return
    
    print(f"文件大小: {len(content)/1024:.2f} KB")
    
    # 正则表达式模式: 匹配ROI状态变化为ON事件 (带有brightest LED标记)
    # 更灵活的模式，不要求完全匹配，只需关键部分匹配
    # 注意: 这里需要根据实际日志格式调整，例如 'LED G(\d+)'
    roi_on_pattern = re.compile(r'INFO - LED G(\d+) state OFF->ON.*?\(brightest LED\)')
    
    # 匹配ROI的状态信息，更灵活的模式
    # 注意: 这里需要根据实际日志格式调整，例如 'LED G(\d+)'
    status_pattern = re.compile(r'INFO - LED G(\d+): Status=(\w+)')
    
    # 提取时间戳的模式
    timestamp_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})')
    
    # 查找所有ROI状态变化为ON的事件
    roi_on_events = []
    for match in roi_on_pattern.finditer(content):
        # 获取包含这个匹配的行
        line_start = content.rfind('\n', 0, match.start()) + 1
        line_end = content.find('\n', match.start())
        if line_end == -1:
            line_end = len(content)
        line = content[line_start:line_end]
        
        # 从行中提取时间戳
        timestamp_match = timestamp_pattern.search(line)
        timestamp = timestamp_match.group(1) if timestamp_match else "unknown"
        
        roi_num = int(match.group(1))
        roi_on_events.append((timestamp, roi_num))
    
    print(f"找到 {len(roi_on_events)} 个ROI点亮事件")
    
    # 查找所有ROI状态信息
    status_events = []
    for match in status_pattern.finditer(content):
        # 获取包含这个匹配的行
        line_start = content.rfind('\n', 0, match.start()) + 1
        line_end = content.find('\n', match.start())
        if line_end == -1:
            line_end = len(content)
        line = content[line_start:line_end]
        
        # 从行中提取时间戳
        timestamp_match = timestamp_pattern.search(line)
        timestamp = timestamp_match.group(1) if timestamp_match else "unknown"
        
        roi_num = int(match.group(1))
        status = match.group(2)
        status_events.append((timestamp, roi_num, status))
    
    print(f"找到 {len(status_events)} 个ROI状态记录")
    
    # 如果没有找到事件，可能是正则表达式匹配有问题
    if len(roi_on_events) == 0:
        print("警告：未找到任何ROI点亮事件，请检查日志格式或正则表达式")
        # 输出文件前200个字符供调试
        print(f"文件开始内容: {content[:200]}...")
        return
    
    # 按时间戳排序
    roi_on_events.sort(key=lambda x: x[0])
    status_events.sort(key=lambda x: x[0])
    
    # 记录完美周期
    perfect_cycles = 0
    current_sequence = []
    perfect_cycle_details = []
    
    # 分析ROI点亮事件序列
    for i in range(len(roi_on_events) - 1):
        curr_time, curr_roi = roi_on_events[i]
        next_time, next_roi = roi_on_events[i + 1]
        
        # 检查顺序
        if not current_sequence:  # 如果是新序列开始
            if curr_roi == 1:  # 必须从ROI 1开始
                is_valid_start = True
            else:
                is_valid_start = False
                print(f"序列必须从ROI 1开始，但从ROI {curr_roi}开始")
                continue
        else:
            # 检查是否是正确的下一个ROI
            expected_next = current_sequence[-1] + 1
            if expected_next > 16:
                expected_next = 1
            
            is_valid_next = (curr_roi == expected_next)
            if not is_valid_next:
                print(f"序列中断: 期望 ROI {expected_next}，但得到 ROI {curr_roi}")
                current_sequence = []
                if curr_roi == 1:  # 如果是ROI 1，可以开始新序列
                    is_valid_start = True
                    current_sequence = [1]
                else:
                    continue
        
        # 检查区间状态
        interval_valid = True
        # 获取当前时间戳到下一个时间戳之间的所有状态信息
        interval_statuses = [event for event in status_events if curr_time <= event[0] < next_time]
        
        # 检查这段时间内是否只有当前ROI处于ON状态
        for ts, roi_num, status in interval_statuses:
            if status == "ON" and roi_num != curr_roi:
                interval_valid = False
                print(f"区间异常: ROI {curr_roi} 到 ROI {next_roi} 之间发现 ROI {roi_num} 为 ON 状态")
                break
        
        if interval_valid:
            # 添加当前ROI到序列
            if not current_sequence or (current_sequence and curr_roi == expected_next):
                current_sequence.append(curr_roi)
            elif curr_roi == 1:  # 如果是ROI 1，开始新序列
                current_sequence = [1]
            
            # 检查是否完成了一个完整周期 (1-16)
            if len(current_sequence) == 16:
                # 验证序列是否为1-16的顺序
                if current_sequence == list(range(1, 17)):
                    perfect_cycles += 1
                    cycle_start = roi_on_events[i-15][0]  # 第一个ROI的时间戳
                    cycle_end = next_time  # 当前周期结束时间
                    perfect_cycle_details.append((perfect_cycles, cycle_start, cycle_end))
                    print(f"发现第 {perfect_cycles} 个完美周期: {current_sequence}")
                    print(f"  开始时间: {cycle_start}")
                    print(f"  结束时间: {cycle_end}")
                else:
                    print(f"发现周期但顺序不正确: {current_sequence}")
                
                # 重置序列开始新的周期检测
                current_sequence = []
        else:
            # 区间异常，重置序列
            current_sequence = []
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "="*50)
    print(f"分析完成！共发现 {perfect_cycles} 个完美顺序点亮周期。")
    print(f"分析耗时: {duration:.2f} 秒")
    print("="*50)
    
    # 输出完美周期详情
    if perfect_cycles > 0:
        print("\n完美周期详情:")
        for idx, start, end in perfect_cycle_details:
            print(f"周期 {idx}: {start} 到 {end}")

if __name__ == "__main__":
    print("="*50)
    print("LED顺序点亮周期分析程序")
    print("="*50)
    analyze_led_cycles() 